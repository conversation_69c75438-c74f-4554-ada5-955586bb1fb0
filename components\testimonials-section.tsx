import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    content:
      "Travail exceptionnel ! L'équipe de Delo BAT Energie a transformé notre maison. Professionnalisme et qualité au rendez-vous.",
    rating: 5,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Gérant de Restaurant",
    content: "Délais respectés et finition parfaite pour notre restaurant. Je recommande vivement leurs services.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Architecte d'Intérieur",
    content: "Collaboration excellente sur plusieurs projets. Leur expertise technique et créative est remarquable.",
    rating: 5,
  },
]

export function TestimonialsSection() {
  return (
    <section className="py-20 bg-card">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-card-foreground mb-4">Témoignages Clients</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">Ce que disent nos clients satisfaits</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-background border-border">
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-primary text-primary" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">"{testimonial.content}"</p>
                <div>
                  <div className="font-semibold text-foreground">{testimonial.name}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
