import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-background via-background to-muted flex items-center">
      {/* Paint stroke decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-8 bg-primary rounded-full transform -rotate-12 opacity-80"></div>
        <div className="absolute top-40 right-20 w-24 h-6 bg-secondary rounded-full transform rotate-45 opacity-70"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-10 bg-accent rounded-full transform -rotate-6 opacity-60"></div>
        <div className="absolute top-1/3 right-1/3 w-20 h-5 bg-chart-4 rounded-full transform rotate-12 opacity-50"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-6 text-balance">
            Transformez Vos <span className="text-primary">Espaces</span> Avec Excellence
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground mb-8 text-pretty">
            Spécialistes en peinture résidentielle et commerciale. De l'imagination à la réalisation, nous donnons vie à
            vos projets.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-4 text-lg">
              Obtenir un Devis
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-foreground text-foreground hover:bg-foreground hover:text-background px-8 py-4 text-lg bg-transparent"
            >
              Voir Nos Projets
            </Button>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">15+</div>
              <div className="text-muted-foreground">Années d'Expérience</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-secondary mb-2">500+</div>
              <div className="text-muted-foreground">Projets Réalisés</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-accent mb-2">100%</div>
              <div className="text-muted-foreground">Satisfaction Client</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
