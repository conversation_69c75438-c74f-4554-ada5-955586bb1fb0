import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-muted text-foreground py-12">
      <div className="container mx-auto px-4">
        {/* Paint stroke decoration */}
        <div className="relative mb-8">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-4 bg-primary rounded-full opacity-80"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="text-xl font-bold mb-4 text-foreground">Delo BAT Energie</h3>
            <p className="text-muted-foreground mb-4">Imaginez, Nous Réalisons</p>
            <p className="text-sm text-muted-foreground">
              Spécialistes en peinture résidentielle et commerciale depuis plus de 15 ans.
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-foreground">Services</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Peinture Résidentielle
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Peinture Commerciale
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Finitions Décoratives
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Conseil Couleur
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-foreground">Liens Utiles</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  À Propos
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Nos Projets
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Témoignages
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-primary transition-colors">
                  Blog
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-foreground">Suivez-Nous</h4>
            <div className="flex space-x-4">
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-muted-foreground hover:text-secondary transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-muted-foreground hover:text-accent transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="text-muted-foreground hover:text-chart-4 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-border pt-8 text-center">
          <p className="text-sm text-muted-foreground">© 2024 Delo BAT Energie. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  )
}
