import { Card } from "@/components/ui/card"

const projects = [
  {
    title: "Villa Moderne",
    category: "Résidentiel",
    image: "/modern-villa-exterior-painting.jpg",
  },
  {
    title: "Bureau Corporate",
    category: "Commercial",
    image: "/corporate-office-interior-painting.jpg",
  },
  {
    title: "Appartement Design",
    category: "Résidentiel",
    image: "/designer-apartment-interior-painting.jpg",
  },
  {
    title: "Restaurant Chic",
    category: "Commercial",
    image: "/elegant-restaurant-interior-painting.jpg",
  },
  {
    title: "Maison Familiale",
    category: "Résidentiel",
    image: "/family-house-exterior-painting.jpg",
  },
  {
    title: "Showroom Auto",
    category: "Commercial",
    image: "/car-showroom-interior-painting.jpg",
  },
]

export function GallerySection() {
  return (
    <section id="projets" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">Nos Réalisations</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Découvrez quelques-uns de nos projets les plus remarquables
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card
              key={index}
              className="group overflow-hidden bg-card border-border hover:shadow-xl transition-all duration-300"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <span className="inline-block px-3 py-1 bg-primary text-primary-foreground text-sm rounded-full mb-2">
                      {project.category}
                    </span>
                    <h3 className="text-foreground font-semibold text-lg">{project.title}</h3>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
