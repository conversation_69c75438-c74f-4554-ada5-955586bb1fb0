import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Paintbrush, Home, Building, Palette } from "lucide-react"

const services = [
  {
    icon: Home,
    title: "Peinture Résidentielle",
    description:
      "Intérieur et extérieur pour votre maison. Finitions de qualité supérieure avec des matériaux écologiques.",
    color: "text-primary",
  },
  {
    icon: Building,
    title: "Peinture Commerciale",
    description:
      "Solutions professionnelles pour bureaux, magasins et espaces commerciaux. Respect des délais garantis.",
    color: "text-secondary",
  },
  {
    icon: Paintbrush,
    title: "Finitions Décoratives",
    description: "Techniques spécialisées : faux-finis, textures, pochoirs et effets artistiques personnalisés.",
    color: "text-accent",
  },
  {
    icon: Palette,
    title: "Conseil Couleur",
    description: "Expertise en harmonies colorées et tendances. Nous vous guidons dans vos choix esthétiques.",
    color: "text-chart-4",
  },
]

export function ServicesSection() {
  return (
    <section id="services" className="py-20 bg-card">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-card-foreground mb-4">Nos Services</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Une gamme complète de services de peinture pour tous vos besoins
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="bg-background border-border hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-3 rounded-full bg-card w-fit">
                  <service.icon className={`w-8 h-8 ${service.color}`} />
                </div>
                <CardTitle className="text-foreground">{service.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground text-center">{service.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
