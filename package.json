{"name": "delo-bat-energie-website", "private": true, "version": "0.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@vercel/analytics": "1.5.0", "class-variance-authority": "0.7.1", "clsx": "^2.1.1", "geist": "1.5.1", "lucide-react": "^0.454.0", "next": "15.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.5.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.17", "typescript": "^5.2.2"}}