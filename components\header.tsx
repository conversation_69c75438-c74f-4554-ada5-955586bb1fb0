import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"

export function Header() {
  return (
    <header className="bg-background border-b border-border fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/images/logo.png" alt="Delo BAT Energie" width={60} height={60} className="rounded-lg" />
            <div>
              <h1 className="text-xl font-bold text-foreground">Delo BAT Energie</h1>
              <p className="text-sm text-muted-foreground">Imaginez, Nous Réalisons</p>
            </div>
          </Link>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-foreground hover:text-primary transition-colors">
              Accueil
            </Link>
            <Link href="/services" className="text-foreground hover:text-primary transition-colors">
              Services
            </Link>
            <Link href="/gallery" className="text-foreground hover:text-primary transition-colors">
              Projets
            </Link>
            <Link href="/#apropos" className="text-foreground hover:text-primary transition-colors">
              À Propos
            </Link>
            <Link href="/contact" className="text-foreground hover:text-primary transition-colors">
              Contact
            </Link>
          </nav>

          <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90">
            <Link href="/contact">Devis Gratuit</Link>
          </Button>
        </div>
      </div>
    </header>
  )
}
