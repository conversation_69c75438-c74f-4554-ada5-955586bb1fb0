import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Phone, Mail, MapPin, Clock } from "lucide-react"
import { submitContactForm } from "@/app/actions/contact"

export function ContactSection() {
  return (
    <section id="contact" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">Contactez-Nous</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Prêt à transformer votre espace ? Obtenez votre devis gratuit dès aujourd'hui
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Demande de Devis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <form action={submitContactForm} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input name="firstName" placeholder="Prénom" className="bg-background text-foreground" required />
                  <Input name="lastName" placeholder="Nom" className="bg-background text-foreground" required />
                </div>
                <Input
                  name="email"
                  placeholder="Email"
                  type="email"
                  className="bg-background text-foreground"
                  required
                />
                <Input name="phone" placeholder="Téléphone" type="tel" className="bg-background text-foreground" />
                <Input name="service" placeholder="Type de projet" className="bg-background text-foreground" />
                <Textarea
                  name="message"
                  placeholder="Décrivez votre projet..."
                  rows={4}
                  className="bg-background text-foreground"
                  required
                />
                <Button type="submit" className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
                  Envoyer la Demande
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <div className="space-y-6">
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Téléphone</h3>
                    <p className="text-muted-foreground">+33 1 23 45 67 89</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-secondary/10 rounded-full">
                    <Mail className="w-6 h-6 text-secondary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Email</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-accent/10 rounded-full">
                    <MapPin className="w-6 h-6 text-accent" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Adresse</h3>
                    <p className="text-muted-foreground">
                      123 Rue de la Peinture
                      <br />
                      75001 Paris, France
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-chart-4/10 rounded-full">
                    <Clock className="w-6 h-6 text-chart-4" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Horaires</h3>
                    <p className="text-muted-foreground">
                      Lun-Ven: 8h-18h
                      <br />
                      Sam: 9h-17h
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
