'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Phone, Mail, MapPin, Clock } from "lucide-react"
import { useState } from "react"

export function ContactSectionMailto() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const subject = `Demande de devis - ${formData.firstName} ${formData.lastName}`
    const body = `
<PERSON><PERSON><PERSON>,

Je souhaite obtenir un devis pour un projet de peinture.

Informations de contact :
- Nom : ${formData.firstName} ${formData.lastName}
- Email : ${formData.email}
- Téléphone : ${formData.phone || 'Non fourni'}
- Type de projet : ${formData.service || 'Non spécifié'}

Description du projet :
${formData.message}

Cordialement,
${formData.firstName} ${formData.lastName}
    `.trim()

    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.location.href = mailtoLink
  }

  return (
    <section id="contact" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">Contactez-Nous</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Prêt à transformer votre espace ? Obtenez votre devis gratuit dès aujourd'hui
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">Demande de Devis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input 
                    name="firstName" 
                    placeholder="Prénom" 
                    className="bg-background text-foreground" 
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required 
                  />
                  <Input 
                    name="lastName" 
                    placeholder="Nom" 
                    className="bg-background text-foreground" 
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required 
                  />
                </div>
                <Input
                  name="email"
                  placeholder="Email"
                  type="email"
                  className="bg-background text-foreground"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
                <Input 
                  name="phone" 
                  placeholder="Téléphone" 
                  type="tel" 
                  className="bg-background text-foreground" 
                  value={formData.phone}
                  onChange={handleInputChange}
                />
                <Input 
                  name="service" 
                  placeholder="Type de projet" 
                  className="bg-background text-foreground" 
                  value={formData.service}
                  onChange={handleInputChange}
                />
                <Textarea
                  name="message"
                  placeholder="Décrivez votre projet..."
                  rows={4}
                  className="bg-background text-foreground"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                />
                <Button type="submit" className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
                  Envoyer par Email
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <div className="space-y-6">
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Téléphone</h3>
                    <p className="text-muted-foreground">+33 1 23 45 67 89</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-secondary/10 rounded-full">
                    <Mail className="w-6 h-6 text-secondary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Email</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-accent/10 rounded-full">
                    <MapPin className="w-6 h-6 text-accent" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Adresse</h3>
                    <p className="text-muted-foreground">
                      123 Rue de la Peinture
                      <br />
                      75001 Paris, France
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-chart-4/10 rounded-full">
                    <Clock className="w-6 h-6 text-chart-4" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground">Horaires</h3>
                    <p className="text-muted-foreground">
                      Lun-Ven: 8h-18h
                      <br />
                      Sam: 9h-17h
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
