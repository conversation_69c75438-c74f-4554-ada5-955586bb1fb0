@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens for Delo BAT Energie dark/blue theme */
  --background: oklch(0.2 0.1 220); /* Dark cyan background */
  --foreground: oklch(1 0 0); /* White text */
  --card: oklch(0.95 0.02 200); /* Light cyan cards */
  --card-foreground: oklch(0.3 0.05 220); /* Dark gray card text */
  --popover: oklch(1 0 0); /* White popover */
  --popover-foreground: oklch(0.2 0.1 220); /* Dark cyan popover text */
  --primary: oklch(0.65 0.2 35); /* Vibrant orange */
  --primary-foreground: oklch(1 0 0); /* White on primary */
  --secondary: oklch(0.55 0.25 15); /* Bright red */
  --secondary-foreground: oklch(1 0 0); /* <PERSON> on secondary */
  --muted: oklch(0.4 0.05 220); /* Muted dark gray */
  --muted-foreground: oklch(1 0 0); /* White muted text */
  --accent: oklch(0.65 0.2 35); /* Orange accent */
  --accent-foreground: oklch(0.2 0.1 220); /* Dark cyan on accent */
  --destructive: oklch(0.55 0.25 15); /* Red for errors */
  --destructive-foreground: oklch(1 0 0); /* White on destructive */
  --border: oklch(0.95 0.02 200); /* Light cyan borders */
  --input: oklch(1 0 0); /* White inputs */
  --ring: oklch(0.65 0.2 35); /* Orange focus ring */
  --chart-1: oklch(0.65 0.2 35); /* Orange chart */
  --chart-2: oklch(0.55 0.25 15); /* Red chart */
  --chart-3: oklch(0.2 0.1 220); /* Dark cyan chart */
  --chart-4: oklch(0.95 0.02 200); /* Light cyan chart */
  --chart-5: oklch(0.4 0.05 220); /* Dark gray chart */
  --radius: 0.5rem;
  --sidebar: oklch(0.2 0.1 220);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.65 0.2 35);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.55 0.25 15);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.95 0.02 200);
  --sidebar-ring: oklch(0.65 0.2 35);
}

.dark {
  /* Dark mode uses same colors since we're already dark themed */
  --background: oklch(0.15 0.1 220);
  --foreground: oklch(1 0 0);
  --card: oklch(0.2 0.1 220);
  --card-foreground: oklch(1 0 0);
  --popover: oklch(0.2 0.1 220);
  --popover-foreground: oklch(1 0 0);
  --primary: oklch(0.65 0.2 35);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.55 0.25 15);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.3 0.05 220);
  --muted-foreground: oklch(0.8 0 0);
  --accent: oklch(0.65 0.2 35);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.55 0.25 15);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.3 0.05 220);
  --input: oklch(0.3 0.05 220);
  --ring: oklch(0.65 0.2 35);
  --chart-1: oklch(0.65 0.2 35);
  --chart-2: oklch(0.55 0.25 15);
  --chart-3: oklch(0.4 0.1 200);
  --chart-4: oklch(0.6 0.15 180);
  --chart-5: oklch(0.7 0.2 60);
  --sidebar: oklch(0.15 0.1 220);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.65 0.2 35);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.55 0.25 15);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.3 0.05 220);
  --sidebar-ring: oklch(0.65 0.2 35);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
