
@tailwind base;
@tailwind components;
@tailwind utilities;



:root {
  /* Updated color tokens for Delo BAT Energie dark/blue theme */
  --background: 220 50% 10%; /* Dark cyan background */
  --foreground: 0 0% 100%; /* White text */
  --card: 200 10% 95%; /* Light cyan cards */
  --card-foreground: 220 25% 30%; /* Dark gray card text */
  --popover: 0 0% 100%; /* White popover */
  --popover-foreground: 220 50% 20%; /* Dark cyan popover text */
  --primary: 35 80% 65%; /* Vibrant orange */
  --primary-foreground: 0 0% 100%; /* White on primary */
  --secondary: 15 90% 55%; /* Bright red */
  --secondary-foreground: 0 0% 100%; /* White on secondary */
  --muted: 220 25% 40%; /* Muted dark gray */
  --muted-foreground: 0 0% 100%; /* White muted text */
  --accent: 35 80% 65%; /* Orange accent */
  --accent-foreground: 220 50% 20%; /* Dark cyan on accent */
  --destructive: 15 90% 55%; /* Red for errors */
  --destructive-foreground: 0 0% 100%; /* White on destructive */
  --border: 200 10% 95%; /* Light cyan borders */
  --input: 0 0% 100%; /* White inputs */
  --ring: 35 80% 65%; /* Orange focus ring */
  --chart-1: 35 80% 65%; /* Orange chart */
  --chart-2: 15 90% 55%; /* Red chart */
  --chart-3: 220 50% 20%; /* Dark cyan chart */
  --chart-4: 200 10% 95%; /* Light cyan chart */
  --chart-5: 220 25% 40%; /* Dark gray chart */
  --radius: 0.5rem;
  --sidebar-background: 220 50% 20%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 35 80% 65%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 15 90% 55%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 200 10% 95%;
  --sidebar-ring: 35 80% 65%;
}

.dark {
  /* Dark mode uses same colors since we're already dark themed */
  --background: 220 50% 15%;
  --foreground: 0 0% 100%;
  --card: 220 50% 20%;
  --card-foreground: 0 0% 100%;
  --popover: 220 50% 20%;
  --popover-foreground: 0 0% 100%;
  --primary: 35 80% 65%;
  --primary-foreground: 0 0% 100%;
  --secondary: 15 90% 55%;
  --secondary-foreground: 0 0% 100%;
  --muted: 220 25% 30%;
  --muted-foreground: 0 0% 80%;
  --accent: 35 80% 65%;
  --accent-foreground: 0 0% 100%;
  --destructive: 15 90% 55%;
  --destructive-foreground: 0 0% 100%;
  --border: 220 25% 30%;
  --input: 220 25% 30%;
  --ring: 35 80% 65%;
  --chart-1: 35 80% 65%;
  --chart-2: 15 90% 55%;
  --chart-3: 200 50% 40%;
  --chart-4: 180 60% 60%;
  --chart-5: 60 70% 70%;
  --sidebar-background: 220 50% 15%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 35 80% 65%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 15 90% 55%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 220 25% 30%;
  --sidebar-ring: 35 80% 65%;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
