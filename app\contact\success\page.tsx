import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function ContactSuccessPage() {
  return (
    <main className="min-h-screen">
      <Header />
      <div className="pt-20 pb-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <div className="bg-card rounded-lg p-8 shadow-lg">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-card-foreground mb-4">Message Envoyé !</h1>
              <p className="text-lg text-muted-foreground mb-6">
                Merci pour votre message. Notre équipe vous contactera dans les plus brefs délais pour discuter de votre
                projet de peinture.
              </p>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">Temps de réponse habituel : 24-48 heures</p>
                <Button asChild className="bg-primary hover:bg-primary/90">
                  <Link href="/">Retour à l'accueil</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  )
}
