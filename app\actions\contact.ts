"use server"

import { redirect } from "next/navigation"
import nodemailer from "nodemailer"

export async function submitContactForm(formData: FormData) {
  const firstName = formData.get("firstName") as string
  const lastName = formData.get("lastName") as string
  const email = formData.get("email") as string
  const phone = formData.get("phone") as string
  const service = formData.get("service") as string
  const message = formData.get("message") as string

  const name = `${firstName} ${lastName}`.trim()

  // Basic validation
  if (!firstName || !lastName || !email || !message) {
    throw new Error("Prénom, nom, email et message sont requis")
  }

  try {
    // Create transporter (you'll need to configure this with your email provider)
    const transporter = nodemailer.createTransporter({
      // Option 1: Gmail (you'll need to enable "App Passwords" in Gmail)
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER, // <EMAIL>
        pass: process.env.EMAIL_PASS, // your app password
      },

      // Option 2: SMTP (for other providers like OVH, Outlook, etc.)
      // host: process.env.SMTP_HOST,
      // port: parseInt(process.env.SMTP_PORT || '587'),
      // secure: false,
      // auth: {
      //   user: process.env.EMAIL_USER,
      //   pass: process.env.EMAIL_PASS,
      // },
    })

    // Email to you (business owner)
    const businessEmail = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>', // Your business email
      subject: `Nouvelle demande de devis - ${name}`,
      html: `
        <h2>Nouvelle demande de devis</h2>
        <p><strong>Nom:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Téléphone:</strong> ${phone || 'Non fourni'}</p>
        <p><strong>Type de projet:</strong> ${service || 'Non spécifié'}</p>
        <p><strong>Message:</strong></p>
        <p>${message.replace(/\n/g, '<br>')}</p>
        <hr>
        <p><small>Envoyé le ${new Date().toLocaleString('fr-FR')}</small></p>
      `,
    }

    // Confirmation email to customer
    const customerEmail = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Confirmation de votre demande - Delo BAT Energie',
      html: `
        <h2>Merci pour votre demande de devis !</h2>
        <p>Bonjour ${firstName},</p>
        <p>Nous avons bien reçu votre demande de devis. Notre équipe vous contactera dans les plus brefs délais.</p>

        <h3>Récapitulatif de votre demande :</h3>
        <p><strong>Nom:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Téléphone:</strong> ${phone || 'Non fourni'}</p>
        <p><strong>Type de projet:</strong> ${service || 'Non spécifié'}</p>
        <p><strong>Message:</strong></p>
        <p>${message.replace(/\n/g, '<br>')}</p>

        <hr>
        <p>Cordialement,<br>L'équipe Delo BAT Energie</p>
        <p>📞 +33 1 23 45 67 89<br>📧 <EMAIL></p>
      `,
    }

    // Send both emails
    await transporter.sendMail(businessEmail)
    await transporter.sendMail(customerEmail)

    console.log("Emails sent successfully for:", name)

  } catch (error) {
    console.error("Error sending email:", error)
    throw new Error("Erreur lors de l'envoi de l'email. Veuillez réessayer.")
  }

  // Redirect to success page
  redirect("/contact/success")
}
