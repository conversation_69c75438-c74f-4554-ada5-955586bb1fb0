"use server"

import { redirect } from "next/navigation"

export async function submitContactForm(formData: FormData) {
  const firstName = formData.get("firstName") as string
  const lastName = formData.get("lastName") as string
  const email = formData.get("email") as string
  const phone = formData.get("phone") as string
  const service = formData.get("service") as string
  const message = formData.get("message") as string

  const name = `${firstName} ${lastName}`.trim()

  // Basic validation
  if (!firstName || !lastName || !email || !message) {
    throw new Error("Prénom, nom, email et message sont requis")
  }

  // Here you would typically send an email or save to database
  // For now, we'll just log the form data
  console.log("Contact form submission:", {
    name,
    email,
    phone,
    service,
    message,
    timestamp: new Date().toISOString(),
  })

  // Simulate processing time
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Redirect to success page
  redirect("/contact/success")
}
